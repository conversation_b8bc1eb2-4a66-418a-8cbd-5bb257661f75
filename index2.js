import "dotenv/config";
import "reflect-metadata";

import { DataSource } from "typeorm";
import express from "express";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { SqlDatabase } from "langchain/sql_db";
import { createSqlQueryChain } from "langchain/chains/sql_db";
import { ConversationSummaryBufferMemory } from "langchain/memory";
import { ConversationChain } from "langchain/chains";
import { PromptTemplate } from "@langchain/core/prompts";

import { prompts, formatPrompt } from "./prompts.js";

const app = express();
app.use(express.json());

// 1. Create TypeORM DataSource
const dataSource = new DataSource({
  type: "postgres",
  url: process.env.DATABASE_URL,
  synchronize: false,
  entities: [
    "User",
    "Vehicle",
    "ServiceRequest",
    "ServiceRequestProvider",
    "ProvidedService",
    "ProvidedServiceAvailability",
    "ProvidedServiceAvailabilitySlot",
    "ProvidedServiceCancellationPolicy",
    "ProvidedServiceType",
  ],
  ssl: true,
  extra: {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  },
  logging: true,
  keepConnectionAlive: true,
});
await dataSource.initialize();
console.log("🔌 Database connected!");
// 2. Create SqlDatabase from DataSource
const db = await SqlDatabase.fromDataSourceParams({
  appDataSource: dataSource,
  includesTables: [
    "User",
    "Vehicle",
    "ServiceRequest",
    "ServiceRequestProvider",
    "ProvidedService",
    "ProvidedServiceAvailability",
    "ProvidedServiceAvailabilitySlot",
    "ProvidedServiceCancellationPolicy",
    "ProvidedServiceType",
  ],
  customDescriptions: {
    Vehicle: `Contains vehicle details for a user. Includes userId, vin, plateNumber, milage, model, engine, etc.`,
    ServiceRequest:
      "ServiceRequest are the requests made by a customer for a service.",
    ServiceRequestProvider: `ServiceRequestProvider are the providers request of a service it's a request created by customer and after accept by provider we add providerId to the ServiceRequest`,
    ProvidedService: `Tracks each service the provider offers, belongs to a user.`,
    ProvidedServiceAvailability: `Defines days when a provider is available.`,
    ProvidedServiceAvailabilitySlot: `Defines time slots for availability within a day.`,
    ProvidedServiceCancellationPolicy: `Defines cancellation fee and window for a service.`,
    ProvidedServiceType: `Links provided services to actual service types and includes pricing.`,
  },
});
console.log("🔌 DB store fetch!");

// 3. Initialize memory store
const memoryMap = new Map();

// Function to clean SQL query from markdown formatting
const cleanSqlQuery = (query) => {
  if (typeof query !== "string") return query;

  // Remove markdown code blocks (```sql ... ```)
  let cleaned = query.replace(/```sql\s*/gi, "").replace(/```\s*$/gi, "");

  // Remove any leading/trailing whitespace
  cleaned = cleaned.trim();

  // Remove any remaining markdown formatting
  cleaned = cleaned.replace(/^`+/, "").replace(/`+$/, "");

  return cleaned;
};

// 4. Handle routes
app.post("/", async (req, res) => {
  const { question, sessionId = "default" } = req.body;
  // const userId = 78;
  // const userId = 137;
  const userId = 153;

  // Memory setup per session
  let memory = memoryMap.get(sessionId);
  if (!memory) {
    memory = new ConversationSummaryBufferMemory({
      llm: new ChatGoogleGenerativeAI({
        model: "gemini-2.0-pro",
        apiKey: process.env.GOOGLE_API_KEY,
        temperature: 0,
      }),
      memoryKey: "history",
      returnMessages: true,
      maxTokenLimit: 1000,
    });
    memoryMap.set(sessionId, memory);
    console.log("New memory created for session:", sessionId);
  }
  // SQL LLM chain with memory
  const naturalLLM = new ChatGoogleGenerativeAI({
    model: "gemini-2.0-flash",
    temperature: 0,
    apiKey: process.env.GOOGLE_API_KEY,
    verbose: true,
  });
  const sqlChain = await createSqlQueryChain({
    llm: naturalLLM,
    db,
    memory,
    dialect: "postgres",
    verbose: true,
  });
  const { history } = await memory.loadMemoryVariables({});

  // Classify if query is SQL
  const classificationPromptText = formatPrompt(prompts.classificationPrompt, {
    history,
    question,
  });
  const classifier = new ChatGoogleGenerativeAI({ model: "gemini-2.0-flash" });
  const classificationResult = await classifier.invoke(
    classificationPromptText
  );
  const isSQLQuery = classificationResult.content.toLowerCase().includes("yes");

  if (isSQLQuery) {
    const contextualPrompt = formatPrompt(prompts.contextualSqlPrompt, {
      history,
      question,
      userId,
    });

    const result = await sqlChain.invoke({ question: contextualPrompt });
    console.log("🧠 Result:", result);
    console.log("🧠 Result Type:", typeof result);

    // Clean the SQL query from markdown formatting
    const sqlQuery = cleanSqlQuery(result);

    console.log("🧠 SQL Query:", sqlQuery);
    const queryResults = await db.run(sqlQuery);

    console.log("🧠 SQL Execution Result:", queryResults);
    const summaryPromptText = formatPrompt(prompts.summaryPrompt, {
      question,
      sqlQuery,
      queryResults: JSON.stringify(queryResults),
      userId,
    });
    const response = await naturalLLM.invoke(summaryPromptText);
    console.log("🗣️ Final Response:", response.content);
    res.json({ message: response.content });
  } else {
    const conversationChain = new ConversationChain({
      llm: naturalLLM,
      memory,
      prompt: new PromptTemplate({
        template: prompts.naturalConversationPrompt.template,
        inputVariables: prompts.naturalConversationPrompt.inputVariables,
      }),
    });
    const naturalResult = await conversationChain.call({ input: question });
    console.log("🧠 Natural Response:", naturalResult.response);
    res.json({ message: naturalResult.response });
  }

  console.log("✅ Execution done.");
});

app.listen(3000, () => {
  console.log("Server is running on port 3000");
});
